import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  // 启用 umi 原生 layout
  layout: {
    title: 'Squads Wallet',
    locale: false,
  },
  routes: [
    {
      path: '/',
      redirect: '/account',
    },
    {
      name: 'Send',
      path: '/send',
      component: './Send',
      icon: 'SendOutlined',
    },
    {
      name: 'Transactions',
      path: '/transactions',
      component: './Transactions',
      icon: 'FileTextOutlined',
    },
    {
      name: 'Members',
      path: '/members',
      component: './Members',
      icon: 'TeamOutlined',
    },
    {
      name: 'Account',
      path: '/account',
      component: './Account',
      icon: 'UserOutlined',
    },
    {
      name: 'Settings',
      path: '/settings',
      component: './Settings',
      icon: 'SettingOutlined',
    },
  ],
  npmClient: 'pnpm',
  define: {
    'process.env': process.env,
  },
  // 禁用 findDOMNode 警告（仅开发环境）
  devtool: process.env.NODE_ENV === 'development' ? 'eval-cheap-module-source-map' : false,
  jsMinifierOptions: {
    target: ['chrome80', 'es2020']
  },
});

