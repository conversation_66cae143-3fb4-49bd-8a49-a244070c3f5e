import { Table } from 'antd'
import { useModel } from '@umijs/max'
import AddressCopy from '@/components/AddressCopy'
import React, { useEffect, useState } from 'react'
import { useWallet } from '@solana/wallet-adapter-react'

const AccountPage: React.FC = () => {
  const { connected } = useWallet()
  const [assets, setAssets] = useState<[]>([])
  const { multisigs, loading, refreshMultisigs } = useModel('multisig')

  useEffect(() => {
    refreshMultisigs()
  }, [])

  useEffect(() => {
    const buildAssets = () => {
      const assetsList: any = []
      if (!connected || multisigs.length === 0) return setAssets([])
      multisigs.forEach(({ vault: { assets } }: any) => {
        assets.forEach(({ symbol, address, balance, value  }: any) => assetsList.push({ symbol, address, balance, value }))
      })
      setAssets(assetsList)
    }
    buildAssets()
  }, [connected, multisigs])

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: any) => record.symbol
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      render: (_: any, record: any) => (
        <div>
          <AddressCopy address={record.address || ''} showFullAddress={true} />
        </div>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number) => v.toFixed(5)
    },
    {
      title: 'Value (USD)',
      dataIndex: 'value',
      key: 'value',
      render: (v: number) => <span style={{ fontWeight: 700, color: '#52c41a' }}>${v.toFixed(2)}</span>
    }
  ]

  return (
    <div>
      <Table columns={columns} dataSource={assets} rowKey={r => r.symbol + r.address} loading={loading} pagination={false} bordered />
    </div>
  )
}

export default AccountPage