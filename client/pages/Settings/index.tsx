import React from 'react'
import { useModel } from '@umijs/max'
import { Card, Descriptions, Space } from 'antd'
import AddressCopy from '@/components/AddressCopy'

const Settings: React.FC = () => {
  const { currentMultisig: multisig, loading } = useModel('multisig')

  return (
    <>
      <Card loading={loading}>
        <Descriptions bordered>
          <Descriptions.Item label="Members" span={3}>
            <div>{multisig?.members.length}</div>
          </Descriptions.Item>
          <Descriptions.Item label="Threshold" span={3}>
            <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
          </Descriptions.Item>
          <Descriptions.Item label="Squad Vault" span={3}>
            <AddressCopy address={multisig?.vault.address || ''} showFullAddress={true} />
          </Descriptions.Item>
          <Descriptions.Item label="Multisig Account" span={3}>
            <AddressCopy address={multisig?.multisigAccount || ''} showFullAddress={true} />
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </>
  )
}

export default Settings
