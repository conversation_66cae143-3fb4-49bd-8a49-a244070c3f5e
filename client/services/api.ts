import { API_BASE_URL } from '@/constants'

class Api {
  private static async request(endpoint: any, options: any = {}) {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      };
      const response = await fetch(`${API_BASE_URL}${endpoint}`, config)
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }
      return (await response.json())
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error)
      throw error
    }
  }

  static async getBlockhash() {
    return this.request('/api/blockhash')
  }

  static async getUserBalance(publicKey: string) {
    return this.request('/api/balance', {
      method: 'POST',
      body: JSON.stringify({ publicKey })
    })
  }

  static async getRecipients() {
    return this.request('/api/tos')
  }

  static async getMultisigs() {
    return this.request('/api/multisigs')
  }

  static async createTransfer(params: any) {
    return this.request('/api/transfer', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }

  static async getTransactions(page: number, pageSize: number) {
    return this.request('/api/transactions', {
      method: 'POST',
      body: JSON.stringify({ page, pageSize })
    })
  }

  static async buildExecuteInstruction(params: any) {
    return this.request('/api/transactions/build-execute', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }

  static async voteTransaction(params: any) {
    return this.request('/api/transactions/vote', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }

  static async executeTransaction(params: any) {
    return this.request('/api/transactions/execute', {
      method: 'POST',
      body: JSON.stringify(params)
    })
  }

  static async cancelTransaction(params: any) {
    return this.request('/api/transactions/cancel', {
      method: 'POST',
      body: JSON.stringify(params),
    })
  }
}

export default Api